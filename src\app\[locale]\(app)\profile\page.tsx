'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { ContractorDetails } from '@/components/ui/contractor-details';
import { IncompleteProfileCard } from '@/components/ui/incomplete-profile-card';
import {
    ContractorOnboarding,
    FullFormValues,
    useCompleteContractorOnboarding,
} from '@/features/contractor-onboarding';
import { useUserWithProfile } from '@/hooks/use-auth';
import { useContractorProfile } from '@/hooks/use-contractor-profile';
import { useForceProfileRefresh } from '@/hooks/use-force-profile-refresh';
import { usePermissions } from '@/hooks/use-permissions';
import {
    AlertTriangle,
    Building,
    CheckCircle,
    Settings,
    UserCheck,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import * as React from 'react';

export default function ProfilePage() {
  const {
    data: user,
    isLoading: userLoading,
    refetch: _refetchUser,
  } = useUserWithProfile();
  const {
    userRole,
    isContractor,
    isJKR,
    isClient,
    isLoading: permissionsLoading,
  } = usePermissions();
  const { data: contractorData, isLoading: contractorDataLoading } =
    useContractorProfile(user?.id, isContractor);
  const completeContractorOnboarding = useCompleteContractorOnboarding();
  const router = useRouter();
  const searchParams = useSearchParams();
  const forceProfileRefresh = useForceProfileRefresh();

  const isLoading = userLoading || permissionsLoading;
  const onboardingCompleted = user?.profile?.onboarding_completed ?? false;

  // Check if user wants to go back to registration mode
  const registrationMode = searchParams?.get('mode') === 'registration'; // Force refresh user data on mount if coming from registration
  React.useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('refresh') === 'true') {
        // Remove the refresh parameter from URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);

        // Force refresh all profile data
        forceProfileRefresh();
      }
    }
  }, [forceProfileRefresh]);

  // Fallback: Auto-refresh if we're a contractor but still showing incomplete after some time
  React.useEffect(() => {
    if (isContractor && !onboardingCompleted && !isLoading) {
      // Check if we just completed onboarding by looking at localStorage (only on client)
      if (typeof localStorage !== 'undefined') {
        const recentlyCompleted = localStorage.getItem(
          'onboarding_just_completed',
        );
        if (recentlyCompleted) {
          // Clear the flag and force refresh after a short delay
          localStorage.removeItem('onboarding_just_completed');
          setTimeout(() => {
            forceProfileRefresh();
          }, 2000);
        }
      }
    }
  }, [isContractor, onboardingCompleted, isLoading, forceProfileRefresh]);

  const handleSubmit = async (values: FullFormValues) => {
    try {
      await completeContractorOnboarding.mutateAsync(values);
    } catch (error) {
      console.error('Contractor onboarding submission error:', error);
    }
  };
  // Loading state with better design
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-4xl mx-auto">
            <Card>
              <CardContent className="flex items-center justify-center py-16">
                <div className="flex flex-col items-center space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                  <div className="text-center space-y-2">
                    <p className="text-lg font-medium text-foreground">
                      Loading your profile...
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Please wait while we fetch your information
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  // Error state - no user data with improved design
  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-4xl mx-auto">
            <Card className="border-destructive/20">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                  <CardTitle className="text-destructive">
                    Unable to Load Profile
                  </CardTitle>
                </div>
                <CardDescription>
                  We couldn&apos;t load your profile information. This might be
                  a temporary issue.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={() => {
                      if (typeof window !== 'undefined') {
                        window.location.reload();
                      }
                    }}
                  >
                    Refresh Page
                  </Button>
                  <Button variant="outline">Go to Dashboard</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  // Contractor onboarding flow with improved design
  // Show onboarding if: not completed OR user explicitly wants to go back to registration
  if (isContractor && (!onboardingCompleted || registrationMode)) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-5xl mx-auto">
            {/* Header Section */}
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold text-foreground mb-2">
                {registrationMode ? 'Update Your Registration' : 'Complete Your Registration'}
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                {registrationMode
                  ? 'Update your contractor profile information. Your previous progress has been saved.'
                  : 'You\'re almost there! Complete your contractor profile to access all features and start collaborating.'
                }
              </p>
            </div>

            {/* Onboarding Form */}
            <ContractorOnboarding onSubmit={handleSubmit} />

            {/* Loading overlay during submission */}
            {completeContractorOnboarding.isPending && (
              <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
                <Card className="w-auto">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <div>
                        <p className="font-medium text-foreground">
                          Completing your registration...
                        </p>
                        <p className="text-sm text-muted-foreground">
                          This may take a few moments
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } // Contractor with completed onboarding - improved responsive design
  if (isContractor && onboardingCompleted) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-6xl mx-auto space-y-8">
            {/* Profile Summary Card with improved responsive design */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-semibold">
                        Contractor Profile Complete
                      </CardTitle>
                      <CardDescription className="text-base">
                        Your contractor registration has been successfully
                        completed and verified.
                      </CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Profile Information */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      Profile Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Full Name
                        </p>
                        <p className="font-medium text-lg">
                          {user.profile?.name}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Email Address
                        </p>
                        <p className="font-medium">{user.profile?.email}</p>
                      </div>
                      {user.profile?.phone_number && (
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Phone Number
                          </p>
                          <p className="font-medium">
                            {user.profile.phone_number}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Role and Status */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      Role & Status
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                        <UserCheck className="h-5 w-5 text-blue-600" />
                        <div>
                          <span className="font-medium">Contractor</span>
                          <p className="text-sm text-muted-foreground">
                            Verified Role
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <span className="font-medium text-green-700 dark:text-green-400">
                            Registration Complete
                          </span>
                          <p className="text-sm text-green-600 dark:text-green-500">
                            All requirements fulfilled
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action buttons with improved responsive layout */}
                <div className="flex flex-col sm:flex-row gap-3 pt-6 mt-6 border-t">
                  <Button className="sm:flex-1 max-w-xs" size="lg">
                    Go to Dashboard
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/settings')}
                    className="sm:flex-1 max-w-xs"
                    size="lg"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Account Settings
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Contractor Details with loading state */}
            {contractorDataLoading ? (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-center">
                    <div className="flex items-center space-x-3">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      <p className="text-sm text-muted-foreground">
                        Loading contractor details...
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : contractorData ? (
              <ContractorDetails contractorData={contractorData} />
            ) : (
              <Card className="w-full max-w-2xl mx-auto border-amber-200 bg-amber-50/50 dark:bg-amber-950/20">
                <CardHeader className="pb-4">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-2 bg-amber-100 dark:bg-amber-900/30 rounded-lg">
                      <AlertTriangle className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                    </div>
                    <div className="flex-1 space-y-2">
                      <CardTitle className="text-xl font-semibold text-amber-800 dark:text-amber-200">
                        Profile Setup Incomplete
                      </CardTitle>
                      <p className="text-amber-700 dark:text-amber-300 text-sm leading-relaxed">
                        Your contractor profile details are incomplete. Please complete your registration to access all features. Your progress has been saved.
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={() => router.push('/profile?mode=registration')}
                      className="flex-1 sm:flex-none bg-amber-600 hover:bg-amber-700 text-white"
                      size="lg"
                    >
                      Back to Registration
                      <Settings className="h-4 w-4 ml-2" />
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => router.push('/dashboard')}
                      className="flex-1 sm:flex-none border-amber-300 text-amber-700 hover:bg-amber-100"
                      size="lg"
                    >
                      Go to Dashboard
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    );
  }
  // JKR user - enhanced responsive design
  if (isJKR) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-5xl mx-auto space-y-8">
            {/* Header Section */}
            <div className="text-center">
              <h1 className="text-3xl font-bold text-foreground mb-2">
                JKR Administrator Profile
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Welcome to the JKR Lift Management System administrator panel.
                Monitor and manage lift maintenance operations across all
                facilities.
              </p>
            </div>

            {/* Profile Summary Card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <Building className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-semibold">
                        JKR Administrator
                      </CardTitle>
                      <CardDescription className="text-base">
                        System administrator with full access privileges
                      </CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Profile Information */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      Profile Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Full Name
                        </p>
                        <p className="font-medium text-lg">
                          {user.profile?.name}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Email Address
                        </p>
                        <p className="font-medium">{user.profile?.email}</p>
                      </div>
                      {user.profile?.phone_number && (
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Phone Number
                          </p>
                          <p className="font-medium">
                            {user.profile.phone_number}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Role and Permissions */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      Role & Permissions
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                        <Building className="h-5 w-5 text-blue-600" />
                        <div>
                          <span className="font-medium text-blue-700 dark:text-blue-400">
                            JKR Administrator
                          </span>
                          <p className="text-sm text-blue-600 dark:text-blue-500">
                            Full system access
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <span className="font-medium">Active Status</span>
                          <p className="text-sm text-muted-foreground">
                            Account verified and active
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action buttons with improved responsive layout */}
                <div className="flex flex-col sm:flex-row gap-3 pt-6 mt-6 border-t">
                  <Button className="sm:flex-1 max-w-xs" size="lg">
                    Go to Dashboard
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/settings')}
                    className="sm:flex-1 max-w-xs"
                    size="lg"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Account Settings
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Administrative Tools Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Administrative Tools</CardTitle>
                <CardDescription>
                  Quick access to administrative functions and system management
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/admin/lifts')}
                  >
                    <Building className="h-6 w-6" />
                    <span className="text-sm font-medium">Manage Lifts</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/admin/contractors')}
                  >
                    <UserCheck className="h-6 w-6" />
                    <span className="text-sm font-medium">
                      Manage Contractors
                    </span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/admin/reports')}
                  >
                    <Settings className="h-6 w-6" />
                    <span className="text-sm font-medium">System Reports</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  // Client user - enhanced responsive design
  if (isClient) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 lg:py-12">
          <div className="w-full max-w-5xl mx-auto space-y-8">
            {/* Header Section */}
            <div className="text-center">
              <h1 className="text-3xl font-bold text-foreground mb-2">
                Client Profile
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Manage your building assets and monitor lift maintenance
                operations. Track service history and schedule maintenance
                requests.
              </p>
            </div>

            {/* Profile Summary Card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-950/20 dark:to-teal-950/20">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <Building className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-semibold">
                        Building Owner/Client
                      </CardTitle>
                      <CardDescription className="text-base">
                        Asset management and maintenance oversight
                      </CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Profile Information */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      Profile Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Full Name
                        </p>
                        <p className="font-medium text-lg">
                          {user.profile?.name}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Email Address
                        </p>
                        <p className="font-medium">{user.profile?.email}</p>
                      </div>
                      {user.profile?.phone_number && (
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Phone Number
                          </p>
                          <p className="font-medium">
                            {user.profile.phone_number}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Role and Status */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-base text-muted-foreground border-b pb-2">
                      Role & Status
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                        <Building className="h-5 w-5 text-green-600" />
                        <div>
                          <span className="font-medium text-green-700 dark:text-green-400">
                            Building Client
                          </span>
                          <p className="text-sm text-green-600 dark:text-green-500">
                            Asset owner
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                          <span className="font-medium">Active Status</span>
                          <p className="text-sm text-muted-foreground">
                            Account verified and active
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action buttons with improved responsive layout */}
                <div className="flex flex-col sm:flex-row gap-3 pt-6 mt-6 border-t">
                  <Button className="sm:flex-1 max-w-xs" size="lg">
                    Go to Dashboard
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/settings')}
                    className="sm:flex-1 max-w-xs"
                    size="lg"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Account Settings
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Client Management Tools Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Asset Management</CardTitle>
                <CardDescription>
                  Quick access to building management and maintenance scheduling
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/buildings')}
                  >
                    <Building className="h-6 w-6" />
                    <span className="text-sm font-medium">My Buildings</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/lifts')}
                  >
                    <Settings className="h-6 w-6" />
                    <span className="text-sm font-medium">Lift Status</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex-col space-y-2"
                    onClick={() => router.push('/maintenance')}
                  >
                    <UserCheck className="h-6 w-6" />
                    <span className="text-sm font-medium">Maintenance</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  // Fallback for unknown role or missing profile data with improved design
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 lg:py-12">
        <div className="w-full max-w-4xl mx-auto">
          <IncompleteProfileCard
            userRole={userRole || undefined}
            isContractor={isContractor}
            onboardingCompleted={onboardingCompleted}
            hasContractorData={!!contractorData}
            customMessage="Your profile information needs attention. Please complete your setup or contact support for assistance."
          />
        </div>
      </div>
    </div>
  );
}
